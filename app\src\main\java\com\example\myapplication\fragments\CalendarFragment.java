package com.example.myapplication.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CalendarView;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapplication.R;
import com.example.myapplication.adapters.TaskAdapter;
import com.example.myapplication.auth.AuthManager;
import com.example.myapplication.database.entities.CategoryEntity;
import com.example.myapplication.database.entities.TaskEntity;
import com.example.myapplication.models.Task;
import com.example.myapplication.models.Category;
import com.example.myapplication.viewmodel.TodoViewModel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

public class CalendarFragment extends Fragment {

    private CalendarView calendarView;
    private RecyclerView recyclerViewDayTasks;
    private TextView tvSelectedDate;
    private TextView tvTaskCount;
    private TextView tvDateNumber;
    private CheckBox cbShowIncompleteOnlyCalendar;
    private TaskAdapter taskAdapter;
    private TodoViewModel todoViewModel;
    private AuthManager authManager;

    private List<TaskEntity> allTaskEntities;
    private List<CategoryEntity> categoryEntities;
    private List<Task> selectedDateTasks;
    private Date selectedDate;
    private boolean showIncompleteOnly = false;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_calendar, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);
        initViewModel();
        setupCalendar();
        setupRecyclerView();
        setupCompletionFilter();
        observeData();

        // Set today as default selected date
        selectedDate = new Date();
        updateSelectedDateDisplay();
    }

    private void initViews(View view) {
        calendarView = view.findViewById(R.id.calendar_view);
        recyclerViewDayTasks = view.findViewById(R.id.recycler_view_day_tasks);
        tvSelectedDate = view.findViewById(R.id.tv_selected_date);
        tvTaskCount = view.findViewById(R.id.tv_task_count);
        tvDateNumber = view.findViewById(R.id.tv_date_number);
        cbShowIncompleteOnlyCalendar = view.findViewById(R.id.cb_show_incomplete_only_calendar);
    }

    private void initViewModel() {
        todoViewModel = new ViewModelProvider(this).get(TodoViewModel.class);
        authManager = AuthManager.getInstance(requireContext());
        selectedDateTasks = new ArrayList<>();
        allTaskEntities = new ArrayList<>();
        categoryEntities = new ArrayList<>();
    }

    /**
     * Quan sát dữ liệu từ database cho user hiện tại
     */
    private void observeData() {
        int currentUserId = authManager.getCurrentUserId();

        // Quan sát danh mục cho user hiện tại trước
        todoViewModel.getCategoriesForUser(currentUserId).observe(getViewLifecycleOwner(), categories -> {
            if (categories != null) {
                categoryEntities = categories;
                // Làm mới danh sách task sau khi load xong categories
                refreshTasksForSelectedDate();
            }
        });

        // Quan sát tasks cho user hiện tại
        todoViewModel.getTasksByUser(currentUserId).observe(getViewLifecycleOwner(), tasks -> {
            if (tasks != null) {
                allTaskEntities = tasks;
                refreshTasksForSelectedDate();
            }
        });
    }

    private void setupCalendar() {
        calendarView.setOnDateChangeListener((view, year, month, dayOfMonth) -> {
            Calendar cal = Calendar.getInstance();
            cal.set(year, month, dayOfMonth);
            selectedDate = cal.getTime();
            updateSelectedDateDisplay();
            refreshTasksForSelectedDate();
        });

        // Set minimum date to today
        calendarView.setMinDate(System.currentTimeMillis());
    }

    private void setupCompletionFilter() {
        cbShowIncompleteOnlyCalendar.setOnCheckedChangeListener((buttonView, isChecked) -> {
            showIncompleteOnly = isChecked;
            refreshTasksForSelectedDate();
        });
    }

    private void setupRecyclerView() {
        taskAdapter = new TaskAdapter(selectedDateTasks);
        recyclerViewDayTasks.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerViewDayTasks.setAdapter(taskAdapter);

        // Thiết lập listener cho click vào task
        taskAdapter.setOnTaskClickListener(new TaskAdapter.OnTaskClickListener() {
            @Override
            public void onTaskClick(Task task) {
                // Mở dialog chi tiết task (cần implement showTaskDetailDialog)
                showTaskDetailDialog(task);
            }

            @Override
            public void onTaskToggle(Task task, boolean isCompleted) {
                // Cập nhật trạng thái hoàn thành task trong database
                updateTaskCompletionInDatabase(task, isCompleted);
            }
        });
    }

    /**
     * Hiển thị dialog chi tiết task
     * @param task Task cần hiển thị chi tiết
     */
    private void showTaskDetailDialog(Task task) {
        // TODO: Implement task detail dialog
        // Có thể sử dụng lại logic từ TaskListFragment
        android.widget.Toast.makeText(getContext(),
            "Chi tiết task: " + task.getTitle(),
            android.widget.Toast.LENGTH_SHORT).show();
    }

    private void refreshTasksForSelectedDate() {
        if (selectedDate == null || allTaskEntities == null || categoryEntities == null) {
            return;
        }

        selectedDateTasks.clear();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        String selectedDateStr = dateFormat.format(selectedDate);

        for (TaskEntity taskEntity : allTaskEntities) {
            if (taskEntity.getDueDate() != null) {
                String taskDateStr = dateFormat.format(taskEntity.getDueDate());
                if (selectedDateStr.equals(taskDateStr)) {
                    // Find corresponding category
                    Category category = findCategoryById(taskEntity.getCategoryId());
                    if (category == null) {
                        category = new Category("0", "Chung", "#2196F3", "📝");
                    }

                    Task task = taskEntity.toTask(category);

                    // Apply completion filter
                    if (!showIncompleteOnly || !task.isCompleted()) {
                        selectedDateTasks.add(task);
                    }
                }
            }
        }

        // Sort tasks: incomplete tasks first, then by priority
        selectedDateTasks.sort((task1, task2) -> {
            // First sort by completion status (incomplete first)
            if (task1.isCompleted() != task2.isCompleted()) {
                return task1.isCompleted() ? 1 : -1;
            }
            // Then sort by priority (HIGH > MEDIUM > LOW)
            return task2.getPriority().ordinal() - task1.getPriority().ordinal();
        });

        taskAdapter.updateTasks(selectedDateTasks);
        updateTaskCount();
    }

    private Category findCategoryById(int categoryId) {
        for (CategoryEntity entity : categoryEntities) {
            if (entity.getId() == categoryId) {
                return entity.toCategory();
            }
        }
        return null;
    }

    private void updateSelectedDateDisplay() {
        if (selectedDate == null) return;

        SimpleDateFormat displayFormat = new SimpleDateFormat("EEEE, dd MMMM yyyy", new Locale("vi", "VN"));
        SimpleDateFormat dayFormat = new SimpleDateFormat("dd", Locale.getDefault());

        tvSelectedDate.setText(displayFormat.format(selectedDate));
        tvDateNumber.setText(dayFormat.format(selectedDate));
    }

    private void updateTaskCount() {
        int totalTasks = selectedDateTasks.size();
        int completedTasks = 0;
        
        for (Task task : selectedDateTasks) {
            if (task.isCompleted()) {
                completedTasks++;
            }
        }
        
        if (totalTasks == 0) {
            tvTaskCount.setText("Không có nhiệm vụ nào");
        } else {
            tvTaskCount.setText(completedTasks + "/" + totalTasks + " nhiệm vụ hoàn thành");
        }
    }

    /**
     * Cập nhật trạng thái hoàn thành task trong database
     * @param task Task cần cập nhật
     * @param isCompleted Trạng thái hoàn thành mới
     */
    private void updateTaskCompletionInDatabase(Task task, boolean isCompleted) {
        if (task.getId() != null) {
            // Cập nhật task object ngay lập tức
            task.setCompleted(isCompleted);
            taskAdapter.updateTask(task);
            updateTaskCount();

            // Cập nhật trong database
            new Thread(() -> {
                try {
                    int taskId = Integer.parseInt(task.getId());
                    TaskEntity taskEntity = new TaskEntity();
                    taskEntity.setId(taskId);
                    taskEntity.setTitle(task.getTitle());
                    taskEntity.setDescription(task.getDescription());
                    taskEntity.setCategoryId(Integer.parseInt(task.getCategory().getId()));
                    taskEntity.setPriority(task.getPriority());
                    taskEntity.setCompleted(isCompleted);
                    taskEntity.setDueDate(task.getDueDate());
                    taskEntity.setStartTime(task.getStartTime());
                    taskEntity.setEndTime(task.getEndTime());
                    taskEntity.setCreatedAt(task.getCreatedAt());
                    taskEntity.setUserId(authManager.getCurrentUserId()); // Thêm user ID

                    todoViewModel.getRepository().updateTaskSync(taskEntity);
                } catch (NumberFormatException e) {
                    // Xử lý ID không hợp lệ
                }
            }).start();
        }
    }
}

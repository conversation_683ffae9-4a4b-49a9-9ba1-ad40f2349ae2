package com.example.myapplication;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;

public interface ApiService {

    // GET /api/tasks - <PERSON><PERSON><PERSON>nh s<PERSON>ch tasks
    @GET(ApiConfig.TASKS_ENDPOINT)
    Call<List<Task>> getTasks();

    // POST /api/tasks - Tạo task mới
    @POST(ApiConfig.TASKS_ENDPOINT)
    Call<Task> addTask(@Body Task task);

    // PUT /api/tasks/{id} - Cập nhật task
    @PUT(ApiConfig.TASKS_ENDPOINT + "/{id}")
    Call<Task> updateTask(@Path("id") String taskId, @Body Task task);

    // DELETE /api/tasks/{id} - Xóa task
    @DELETE(ApiConfig.TASKS_ENDPOINT + "/{id}")
    Call<Void> deleteTask(@Path("id") String taskId);
}

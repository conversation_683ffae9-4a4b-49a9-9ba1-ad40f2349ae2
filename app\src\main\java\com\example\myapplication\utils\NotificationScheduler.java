package com.example.myapplication.utils;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

/**
 * Scheduler để lên lịch thông báo định kỳ
 */
public class NotificationScheduler {
    
    private static final int NOTIFICATION_REQUEST_CODE = 1001;
    
    /**
     * Lên lịch thông báo kiểm tra định kỳ (mỗi 30 phút)
     */
    public static void schedulePeriodicNotifications(Context context) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        
        Intent intent = new Intent(context, NotificationReceiver.class);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context, 
            NOTIFICATION_REQUEST_CODE, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        // <PERSON>ên lịch chạy mỗi 5 phút để test (sau này có thể tăng lên 30 phút)
        long intervalMillis = 5 * 60 * 1000; // 5 phút
        long triggerAtMillis = System.currentTimeMillis() + intervalMillis;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP, 
                triggerAtMillis, 
                pendingIntent
            );
        } else {
            alarmManager.setRepeating(
                AlarmManager.RTC_WAKEUP, 
                triggerAtMillis, 
                intervalMillis, 
                pendingIntent
            );
        }
        
        android.util.Log.d("NotificationScheduler", "Scheduled periodic notifications");
    }
    
    /**
     * Hủy lịch thông báo
     */
    public static void cancelPeriodicNotifications(Context context) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        
        Intent intent = new Intent(context, NotificationReceiver.class);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context, 
            NOTIFICATION_REQUEST_CODE, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        alarmManager.cancel(pendingIntent);
        android.util.Log.d("NotificationScheduler", "Cancelled periodic notifications");
    }
    
    /**
     * BroadcastReceiver để nhận alarm và chạy notification worker
     */
    public static class NotificationReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            android.util.Log.d("NotificationReceiver", "Received alarm, checking notifications");
            
            // Chạy notification worker trong background thread
            new Thread(() -> {
                com.example.myapplication.workers.NotificationWorker.checkAndSendNotifications(context);
                
                // Lên lịch lần tiếp theo (cho Android 6.0+)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    schedulePeriodicNotifications(context);
                }
            }).start();
        }
    }
}

package com.example.myapplication;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.ArrayList;
import java.util.List;

import com.example.myapplication.databinding.FragmentFirstBinding;

public class FirstFragment extends Fragment {

    private FragmentFirstBinding binding;
    private SimpleTaskAdapter taskAdapter;
    private List<SimpleTask> taskList;
    @Override
    public View onCreateView(
            LayoutInflater inflater, ViewGroup container,
            Bundle savedInstanceState
    ) {

        binding = FragmentFirstBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Initialize task list with sample data
        initializeSampleData();

        // Setup RecyclerView
        setupRecyclerView();

        // Setup add button
        binding.fabAddTask.setOnClickListener(v -> showAddTaskDialog());

        // Xử lý click nút tìm kiếm
        binding.btnSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Tìm kiếm task", Toast.LENGTH_SHORT).show();
            }
        });

        // Xử lý click các nút chọn chủ đề
        binding.btnAllTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                currentCategoryFilter = "Tất cả";
                observeTasks();
                Toast.makeText(getContext(), "Hiển thị tất cả task", Toast.LENGTH_SHORT).show();
            }
        });

        binding.btnPersonalTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                currentCategoryFilter = "Cá nhân";
                observeTasks();
                Toast.makeText(getContext(), "Hiển thị task cá nhân", Toast.LENGTH_SHORT).show();
            }
        });

        binding.btnWorkTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                currentCategoryFilter = "Công việc";
                observeTasks();
                Toast.makeText(getContext(), "Hiển thị task công việc", Toast.LENGTH_SHORT).show();
            }
        });

        binding.btnAddCategory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Thêm chủ đề mới", Toast.LENGTH_SHORT).show();
            }
        });

        // Xử lý click nút thêm task
        binding.fabAddTask.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showAddTaskDialog();
            }
        });

        // Xử lý click navigation
        binding.navTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // Đã ở trang Tasks rồi, không cần làm gì
            }
        });

        binding.navCalendar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                NavHostFragment.findNavController(FirstFragment.this)
                        .navigate(R.id.action_FirstFragment_to_SecondFragment);
            }
        });

        binding.navProfile.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Chuyển đến trang Của tôi", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupRecyclerView() {
        taskAdapter = new SimpleTaskAdapter(new ArrayList<>());

        binding.recyclerTasks.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerTasks.setAdapter(taskAdapter);

        // Set click listener for task items
        taskAdapter.setOnTaskClickListener(new SimpleTaskAdapter.OnTaskClickListener() {
            @Override
            public void onTaskClick(Task task) {
                Toast.makeText(getContext(), "Clicked: " + task.getTitle(), Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onTaskToggle(Task task, boolean isCompleted) {
                task.setCompleted(isCompleted);
                TaskRoomDatabase.databaseWriteExecutor.execute(() -> {
                    taskDao.update(task);
                });
                Toast.makeText(getContext(),
                    isCompleted ? "Task completed!" : "Task uncompleted!",
                    Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void observeTasks() {
        taskDao.getAllTasks().observe(getViewLifecycleOwner(), new Observer<List<Task>>() {
            @Override
            public void onChanged(List<Task> tasks) {
                taskAdapter.updateTasks(tasks);
            }
        });
    }

    private void showAddTaskDialog() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(getContext());
        builder.setTitle("Thêm Task Mới");

        View dialogView = getLayoutInflater().inflate(android.R.layout.simple_list_item_2, null);

        // Create simple input fields
        android.widget.EditText titleInput = new android.widget.EditText(getContext());
        titleInput.setHint("Nhập tiêu đề task");

        android.widget.EditText descInput = new android.widget.EditText(getContext());
        descInput.setHint("Nhập mô tả task");

        android.widget.LinearLayout layout = new android.widget.LinearLayout(getContext());
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.addView(titleInput);
        layout.addView(descInput);

        builder.setView(layout);

        builder.setPositiveButton("Thêm", (dialog, which) -> {
            String title = titleInput.getText().toString().trim();
            String description = descInput.getText().toString().trim();

            if (!title.isEmpty()) {
                addNewTask(title, description);
                Toast.makeText(getContext(), "Đã thêm task mới!", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(getContext(), "Vui lòng nhập tiêu đề!", Toast.LENGTH_SHORT).show();
            }
        });

        builder.setNegativeButton("Hủy", null);
        builder.show();
    }

    private void addNewTask(String title, String description) {
        Task newTask = new Task(
            String.valueOf(System.currentTimeMillis()),
            title,
            description,
            false
        );

        TaskRoomDatabase.databaseWriteExecutor.execute(() -> {
            taskDao.insert(newTask);
        });
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

}
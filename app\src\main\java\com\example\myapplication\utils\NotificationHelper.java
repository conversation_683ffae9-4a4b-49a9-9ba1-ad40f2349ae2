package com.example.myapplication.utils;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;

import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import com.example.myapplication.R;
import com.example.myapplication.MainActivity;

/**
 * Helper class để quản lý thông báo
 */
public class NotificationHelper {
    
    private static final String CHANNEL_ID = "todo_notifications";
    private static final String CHANNEL_NAME = "Todo Notifications";
    private static final String CHANNEL_DESCRIPTION = "Thông báo nhắc nhở nhiệm vụ";
    
    private static final String PREFS_NAME = "notification_prefs";
    private static final String KEY_NOTIFICATIONS_ENABLED = "notifications_enabled";
    
    private Context context;
    private NotificationManagerCompat notificationManager;
    private SharedPreferences prefs;
    
    public NotificationHelper(Context context) {
        this.context = context;
        this.notificationManager = NotificationManagerCompat.from(context);
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        createNotificationChannel();
    }
    
    /**
     * Tạo notification channel
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            
            NotificationManager manager = context.getSystemService(NotificationManager.class);
            manager.createNotificationChannel(channel);
        }
    }
    
    /**
     * Kiểm tra xem thông báo có được bật không
     */
    public boolean isNotificationsEnabled() {
        return prefs.getBoolean(KEY_NOTIFICATIONS_ENABLED, true); // Mặc định là bật
    }
    
    /**
     * Bật/tắt thông báo
     */
    public void setNotificationsEnabled(boolean enabled) {
        prefs.edit().putBoolean(KEY_NOTIFICATIONS_ENABLED, enabled).apply();
    }
    
    /**
     * Hiển thị thông báo nhắc nhở nhiệm vụ
     */
    public void showTaskReminder(String taskTitle, String taskDescription, int taskId) {
        if (!isNotificationsEnabled()) {
            return; // Không hiển thị nếu thông báo bị tắt
        }

        // Tạo intent để mở app khi click vào thông báo
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        intent.putExtra("task_id", taskId);

        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            taskId,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        // Tạo notification
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle("⏰ Nhắc nhở nhiệm vụ")
                .setContentText(taskTitle)
                .setStyle(new NotificationCompat.BigTextStyle()
                        .bigText(taskDescription != null && !taskDescription.isEmpty()
                                ? taskDescription : taskTitle))
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setDefaults(NotificationCompat.DEFAULT_ALL);

        // Hiển thị thông báo
        notificationManager.notify(taskId, builder.build());
    }

    /**
     * Hiển thị thông báo nhắc nhở trước 1 tiếng bắt đầu
     */
    public void showTaskStartReminder(String taskTitle, String taskDescription, int taskId) {
        if (!isNotificationsEnabled()) {
            return;
        }

        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        intent.putExtra("task_id", taskId);

        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            taskId + 1000, // Offset để tránh trùng ID
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle("🚀 Sắp bắt đầu nhiệm vụ")
                .setContentText("Còn 1 tiếng nữa: " + taskTitle)
                .setStyle(new NotificationCompat.BigTextStyle()
                        .bigText("Nhiệm vụ '" + taskTitle + "' sẽ bắt đầu trong 1 tiếng. Hãy chuẩn bị!"))
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setDefaults(NotificationCompat.DEFAULT_ALL);

        notificationManager.notify(taskId + 1000, builder.build());
    }

    /**
     * Hiển thị thông báo nhắc nhở trước 1 tiếng kết thúc
     */
    public void showTaskEndReminder(String taskTitle, String taskDescription, int taskId) {
        if (!isNotificationsEnabled()) {
            return;
        }

        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        intent.putExtra("task_id", taskId);

        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            taskId + 2000, // Offset để tránh trùng ID
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle("⏳ Sắp hết thời gian")
                .setContentText("Còn 1 tiếng: " + taskTitle)
                .setStyle(new NotificationCompat.BigTextStyle()
                        .bigText("Nhiệm vụ '" + taskTitle + "' sẽ kết thúc trong 1 tiếng. Hãy hoàn thành!"))
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setDefaults(NotificationCompat.DEFAULT_ALL);

        notificationManager.notify(taskId + 2000, builder.build());
    }

    /**
     * Hiển thị thông báo nhiệm vụ quá hạn
     */
    public void showOverdueTaskReminder(String taskTitle, String taskDescription, int taskId) {
        if (!isNotificationsEnabled()) {
            return;
        }

        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        intent.putExtra("task_id", taskId);

        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            taskId + 3000, // Offset để tránh trùng ID
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle("🚨 Nhiệm vụ quá hạn")
                .setContentText("Quá hạn: " + taskTitle)
                .setStyle(new NotificationCompat.BigTextStyle()
                        .bigText("Nhiệm vụ '" + taskTitle + "' đã quá hạn. Hãy hoàn thành ngay!"))
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setDefaults(NotificationCompat.DEFAULT_ALL);

        notificationManager.notify(taskId + 3000, builder.build());
    }
    
    /**
     * Hiển thị thông báo chúc mừng hoàn thành nhiệm vụ
     */
    public void showTaskCompletedNotification(String taskTitle) {
        if (!isNotificationsEnabled()) {
            return;
        }
        
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context, 
            0, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle("🎉 Chúc mừng!")
                .setContentText("Bạn đã hoàn thành: " + taskTitle)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setDefaults(NotificationCompat.DEFAULT_ALL);
        
        notificationManager.notify((int) System.currentTimeMillis(), builder.build());
    }
    
    /**
     * Hiển thị thông báo tổng kết hàng ngày
     */
    public void showDailySummaryNotification(int totalTasks, int completedTasks) {
        if (!isNotificationsEnabled()) {
            return;
        }
        
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context, 
            0, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        String title = "📊 Tổng kết ngày hôm nay";
        String content = String.format("Bạn đã hoàn thành %d/%d nhiệm vụ", completedTasks, totalTasks);
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle(title)
                .setContentText(content)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true);
        
        notificationManager.notify(9999, builder.build());
    }
    
    /**
     * Hủy tất cả thông báo
     */
    public void cancelAllNotifications() {
        notificationManager.cancelAll();
    }
    
    /**
     * Hủy thông báo cụ thể
     */
    public void cancelNotification(int notificationId) {
        notificationManager.cancel(notificationId);
    }
}

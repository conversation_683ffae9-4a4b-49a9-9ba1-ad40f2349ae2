1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:7:5-77
13-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:8:5-66
14-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
15-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
16-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:10:5-74
16-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:10:22-71
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
17-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:22-65
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
18-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
19-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
19-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
20
21    <permission
21-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
22        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
26
27    <application
27-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:12:5-60:19
28        android:allowBackup="true"
28-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:13:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
30        android:dataExtractionRules="@xml/data_extraction_rules"
30-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:14:9-65
31        android:debuggable="true"
32        android:fullBackupContent="@xml/backup_rules"
32-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:15:9-54
33        android:icon="@mipmap/ic_launcher"
33-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:16:9-43
34        android:label="@string/app_name"
34-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:17:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:18:9-54
36        android:supportsRtl="true"
36-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:19:9-35
37        android:testOnly="true"
38        android:theme="@style/Theme.MyApplication" >
38-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:20:9-51
39        <activity
39-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:22:9-36:20
40            android:name="com.example.myapplication.MainActivity"
40-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:23:13-41
41            android:exported="true"
41-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:24:13-36
42            android:label="@string/app_name"
42-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:25:13-45
43            android:theme="@style/Theme.MyApplication.NoActionBar" >
43-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:26:13-67
44            <intent-filter>
44-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:27:13-31:29
45                <action android:name="android.intent.action.MAIN" />
45-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:28:17-69
45-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:28:25-66
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:30:17-77
47-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:30:27-74
48            </intent-filter>
49
50            <meta-data
50-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:33:13-35:36
51                android:name="android.app.lib_name"
51-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:34:17-52
52                android:value="" />
52-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:35:17-33
53        </activity>
54        <activity
54-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:38:9-41:70
55            android:name="com.example.myapplication.activities.AuthActivity"
55-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:39:13-52
56            android:exported="false"
56-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:40:13-37
57            android:theme="@style/Theme.MyApplication.NoActionBar" />
57-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:41:13-67
58
59        <!-- <activity -->
60        <!-- android:name=".TaskDetailActivity" -->
61        <!-- android:exported="false" -->
62        <!-- android:label="Chi tiết Task" -->
63        <!-- android:parentActivityName=".MainActivity" -->
64        <!-- android:theme="@style/Theme.MyApplication.NoActionBar"> -->
65        <!-- <meta-data -->
66        <!-- android:name="android.support.PARENT_ACTIVITY" -->
67        <!-- android:value=".MainActivity" /> -->
68        <!-- </activity> -->
69
70
71        <!-- Notification Receiver -->
72        <receiver
72-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:55:9-58:40
73            android:name="com.example.myapplication.utils.NotificationScheduler$NotificationReceiver"
73-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:56:13-77
74            android:enabled="true"
74-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:57:13-35
75            android:exported="false" />
75-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:58:13-37
76
77        <provider
77-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
78            android:name="androidx.startup.InitializationProvider"
78-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
79            android:authorities="com.example.myapplication.androidx-startup"
79-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
80            android:exported="false" >
80-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
81            <meta-data
81-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
82                android:name="androidx.work.WorkManagerInitializer"
82-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
83                android:value="androidx.startup" />
83-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
84            <meta-data
84-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
85                android:name="androidx.emoji2.text.EmojiCompatInitializer"
85-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
86                android:value="androidx.startup" />
86-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
87            <meta-data
87-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
88                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
88-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
89                android:value="androidx.startup" />
89-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
90            <meta-data
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
91                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
92                android:value="androidx.startup" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
93        </provider>
94
95        <service
95-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
96            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
96-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
97            android:directBootAware="false"
97-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
98            android:enabled="@bool/enable_system_alarm_service_default"
98-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
99            android:exported="false" />
99-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
100        <service
100-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
101            android:name="androidx.work.impl.background.systemjob.SystemJobService"
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
102            android:directBootAware="false"
102-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
103            android:enabled="@bool/enable_system_job_service_default"
103-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
104            android:exported="true"
104-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
105            android:permission="android.permission.BIND_JOB_SERVICE" />
105-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
106        <service
106-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
107            android:name="androidx.work.impl.foreground.SystemForegroundService"
107-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
108            android:directBootAware="false"
108-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
109            android:enabled="@bool/enable_system_foreground_service_default"
109-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
110            android:exported="false" />
110-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
111
112        <receiver
112-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
113            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
113-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
114            android:directBootAware="false"
114-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
115            android:enabled="true"
115-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
116            android:exported="false" />
116-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
117        <receiver
117-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
118            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
118-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
119            android:directBootAware="false"
119-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
120            android:enabled="false"
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
121            android:exported="false" >
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
122            <intent-filter>
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
123                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
124                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
125            </intent-filter>
126        </receiver>
127        <receiver
127-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
128            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
128-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
129            android:directBootAware="false"
129-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
130            android:enabled="false"
130-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
131            android:exported="false" >
131-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
132            <intent-filter>
132-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
133                <action android:name="android.intent.action.BATTERY_OKAY" />
133-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
133-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
134                <action android:name="android.intent.action.BATTERY_LOW" />
134-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
134-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
135            </intent-filter>
136        </receiver>
137        <receiver
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
138            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
139            android:directBootAware="false"
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
140            android:enabled="false"
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
141            android:exported="false" >
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
142            <intent-filter>
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
143                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
144                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
145            </intent-filter>
146        </receiver>
147        <receiver
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
148            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
149            android:directBootAware="false"
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
150            android:enabled="false"
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
151            android:exported="false" >
151-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
152            <intent-filter>
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
153                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
154            </intent-filter>
155        </receiver>
156        <receiver
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
157            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
157-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
158            android:directBootAware="false"
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
159            android:enabled="false"
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
160            android:exported="false" >
160-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
161            <intent-filter>
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
162                <action android:name="android.intent.action.BOOT_COMPLETED" />
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
163                <action android:name="android.intent.action.TIME_SET" />
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
164                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
165            </intent-filter>
166        </receiver>
167        <receiver
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
168            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
169            android:directBootAware="false"
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
170            android:enabled="@bool/enable_system_alarm_service_default"
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
171            android:exported="false" >
171-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
172            <intent-filter>
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
173                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
174            </intent-filter>
175        </receiver>
176        <receiver
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
177            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
177-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
179            android:enabled="true"
179-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
180            android:exported="true"
180-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
181            android:permission="android.permission.DUMP" >
181-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
182            <intent-filter>
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
183                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\71ee7b76a5d3def0eb7785f769b3174c\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
184            </intent-filter>
185        </receiver>
186
187        <service
187-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
188            android:name="androidx.room.MultiInstanceInvalidationService"
188-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
189            android:directBootAware="true"
189-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
190            android:exported="false" />
190-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
191
192        <uses-library
192-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
193            android:name="androidx.window.extensions"
193-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
194            android:required="false" />
194-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
195        <uses-library
195-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
196            android:name="androidx.window.sidecar"
196-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
197            android:required="false" />
197-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
198
199        <receiver
199-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
200            android:name="androidx.profileinstaller.ProfileInstallReceiver"
200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
201            android:directBootAware="false"
201-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
202            android:enabled="true"
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
203            android:exported="true"
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
204            android:permission="android.permission.DUMP" >
204-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
205            <intent-filter>
205-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
206                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
207            </intent-filter>
208            <intent-filter>
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
209                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
210            </intent-filter>
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
212                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
213            </intent-filter>
214            <intent-filter>
214-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
215                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
216            </intent-filter>
217        </receiver>
218    </application>
219
220</manifest>

package com.example.myapplication.fragments;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapplication.R;
import com.example.myapplication.activities.AuthActivity;
import com.example.myapplication.adapters.CategoryStatsAdapter;
import com.example.myapplication.auth.AuthManager;
import com.example.myapplication.database.TodoDatabase;
import com.example.myapplication.database.entities.TaskEntity;
import com.example.myapplication.database.entities.CategoryEntity;
import com.example.myapplication.database.entities.UserEntity;
import com.example.myapplication.models.User;
import com.example.myapplication.viewmodel.TodoViewModel;
import com.example.myapplication.utils.PasswordUtils;
import com.example.myapplication.utils.NotificationHelper;
import com.example.myapplication.utils.NotificationScheduler;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * ProfileFragment - Fragment hiển thị thông tin hồ sơ người dùng
 * Chức năng chính:
 * - Hiển thị thông tin cá nhân (tên, email)
 * - Hiển thị thống kê task (tổng số, hoàn thành, chưa hoàn thành)
 * - Hiển thị thống kê theo category
 * - Quản lý cài đặt (thông báo, đổi thông tin, đăng xuất)
 */
public class ProfileFragment extends Fragment {

    // UI Components - Các thành phần giao diện
    private TextView tvUserName, tvUserEmail; // Hiển thị tên và email người dùng
    private TextView tvTotalTasks, tvCompletedTasks, tvPendingTasks, tvCompletionPercentage; // Thống kê task
    private RecyclerView recyclerCategoryStats; // RecyclerView hiển thị thống kê category
    private LinearLayout layoutChangePassword, layoutNotifications, layoutLogout; // Các layout setting
    private Switch switchNotifications; // Switch bật/tắt thông báo

    // Data
    private AuthManager authManager;
    private TodoViewModel todoViewModel;
    private User currentUser;
    private CategoryStatsAdapter categoryStatsAdapter;
    private List<TaskEntity> currentTasks; // Lưu tasks hiện tại để tính category stats
    private List<CategoryEntity> currentCategories; // Lưu categories hiện tại
    private NotificationHelper notificationHelper;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_profile, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Khởi tạo các components
        authManager = AuthManager.getInstance(requireContext());
        todoViewModel = new ViewModelProvider(this).get(TodoViewModel.class);
        currentUser = authManager.getCurrentUser();
        notificationHelper = new NotificationHelper(requireContext());

        initViews(view);
        setupClickListeners();
        loadStatistics();
        updateUserInfo();
    }

    /**
     * Khởi tạo các view components
     * @param view Root view của fragment
     */
    private void initViews(View view) {
        // User info
        tvUserName = view.findViewById(R.id.tv_user_name);
        tvUserEmail = view.findViewById(R.id.tv_user_email);

        // Statistics
        tvTotalTasks = view.findViewById(R.id.tv_total_tasks);
        tvCompletedTasks = view.findViewById(R.id.tv_completed_tasks);
        tvPendingTasks = view.findViewById(R.id.tv_pending_tasks);
        tvCompletionPercentage = view.findViewById(R.id.tv_completion_percentage);
        recyclerCategoryStats = view.findViewById(R.id.recycler_category_stats);

        // Settings
        layoutChangePassword = view.findViewById(R.id.layout_change_password);
        layoutNotifications = view.findViewById(R.id.layout_notifications);
        layoutLogout = view.findViewById(R.id.layout_logout);
        switchNotifications = view.findViewById(R.id.switch_notifications);

        // Setup RecyclerView
        recyclerCategoryStats.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerCategoryStats.setNestedScrollingEnabled(true);
        recyclerCategoryStats.setHasFixedSize(false); // Cho phép thay đổi kích thước
    }

    /**
     * Thiết lập các click listeners cho UI components
     */
    private void setupClickListeners() {
        // Edit Profile (thay vì Change Password)
        layoutChangePassword.setOnClickListener(v -> showEditProfileDialog());

        // Notifications toggle
        // Set trạng thái hiện tại của switch
        switchNotifications.setChecked(notificationHelper.isNotificationsEnabled());

        switchNotifications.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // Lưu setting thông báo
            notificationHelper.setNotificationsEnabled(isChecked);

            if (isChecked) {
                Toast.makeText(getContext(), "🔔 Đã bật thông báo", Toast.LENGTH_SHORT).show();

                // Bắt đầu lên lịch thông báo định kỳ
                NotificationScheduler.schedulePeriodicNotifications(getContext());

                // Hiển thị thông báo test
                notificationHelper.showTaskReminder("Thông báo đã được bật!",
                    "Bạn sẽ nhận được thông báo nhắc nhở trước 1 tiếng bắt đầu/kết thúc nhiệm vụ", 0);

                // Test thông báo nhắc nhở tasks ngay lập tức
                testNotificationReminder();
            } else {
                Toast.makeText(getContext(), "🔕 Đã tắt thông báo", Toast.LENGTH_SHORT).show();

                // Hủy lịch thông báo định kỳ
                NotificationScheduler.cancelPeriodicNotifications(getContext());

                // Hủy tất cả thông báo hiện tại
                notificationHelper.cancelAllNotifications();
            }
        });

        // Logout
        layoutLogout.setOnClickListener(v -> showLogoutConfirmDialog());
    }

    /**
     * Cập nhật thông tin user
     */
    private void updateUserInfo() {
        if (authManager.isLoggedIn() && currentUser != null) {
            tvUserName.setText(currentUser.getUsername());
            tvUserEmail.setText(currentUser.getEmail());
        } else {
            // Nếu chưa login, chuyển về AuthActivity
            navigateToAuth();
        }
    }

    /**
     * Load thống kê từ database
     */
    private void loadStatistics() {
        int currentUserId = authManager.getCurrentUserId();

        // Observe tasks để tính thống kê
        todoViewModel.getTasksByUser(currentUserId).observe(getViewLifecycleOwner(), tasks -> {
            if (tasks != null) {
                updateStatistics(tasks);
                // Cập nhật category stats nếu đã có categories
                if (currentCategories != null) {
                    updateCategoryStatistics(currentCategories);
                }
            }
        });

        // Observe categories để hiển thị thống kê theo danh mục
        todoViewModel.getCategoriesForUser(currentUserId).observe(getViewLifecycleOwner(), categories -> {
            if (categories != null) {
                currentCategories = categories; // Lưu để dùng khi tasks update
                // Cập nhật category stats nếu đã có tasks
                if (currentTasks != null) {
                    updateCategoryStatistics(categories);
                }
            }
        });
    }

    /**
     * Cập nhật thống kê tổng quan
     * @param tasks Danh sách tasks
     */
    private void updateStatistics(List<TaskEntity> tasks) {
        this.currentTasks = tasks; // Lưu để dùng cho category stats

        int totalTasks = tasks.size();
        int completedTasks = 0;

        for (TaskEntity task : tasks) {
            if (task.isCompleted()) {
                completedTasks++;
            }
        }

        int pendingTasks = totalTasks - completedTasks;

        // Cập nhật UI
        tvTotalTasks.setText(String.valueOf(totalTasks));
        tvCompletedTasks.setText(String.valueOf(completedTasks));
        tvPendingTasks.setText(String.valueOf(pendingTasks));

        // Tính phần trăm hoàn thành
        int percentage = totalTasks > 0 ? (completedTasks * 100) / totalTasks : 0;
        tvCompletionPercentage.setText(percentage + "%");
    }

    /**
     * Cập nhật thống kê theo danh mục
     * @param categories Danh sách categories
     */
    private void updateCategoryStatistics(List<CategoryEntity> categories) {
        if (currentTasks == null || categories == null) {
            android.util.Log.d("ProfileFragment", "Cannot update category stats - missing data");
            return;
        }

        android.util.Log.d("ProfileFragment", "Updating category stats with " + categories.size() + " categories and " + currentTasks.size() + " tasks");

        // Tính toán thống kê theo category
        Map<Integer, Integer> categoryTaskCount = new HashMap<>();
        Map<Integer, Integer> categoryCompletedCount = new HashMap<>();

        for (TaskEntity task : currentTasks) {
            int categoryId = task.getCategoryId();
            categoryTaskCount.put(categoryId, categoryTaskCount.getOrDefault(categoryId, 0) + 1);

            if (task.isCompleted()) {
                categoryCompletedCount.put(categoryId, categoryCompletedCount.getOrDefault(categoryId, 0) + 1);
            }
        }

        android.util.Log.d("ProfileFragment", "Category task counts: " + categoryTaskCount.toString());

        // Cập nhật adapter
        if (categoryStatsAdapter == null) {
            categoryStatsAdapter = new CategoryStatsAdapter(categories, categoryTaskCount, categoryCompletedCount);
            recyclerCategoryStats.setAdapter(categoryStatsAdapter);
            android.util.Log.d("ProfileFragment", "Created new CategoryStatsAdapter with " + categoryStatsAdapter.getItemCount() + " items");
        } else {
            categoryStatsAdapter.updateStats(categories, categoryTaskCount, categoryCompletedCount);
            android.util.Log.d("ProfileFragment", "Updated existing CategoryStatsAdapter with " + categoryStatsAdapter.getItemCount() + " items");
        }

        // Force refresh RecyclerView
        recyclerCategoryStats.post(() -> {
            if (categoryStatsAdapter != null) {
                categoryStatsAdapter.notifyDataSetChanged();
                android.util.Log.d("ProfileFragment", "Force refreshed RecyclerView");
            }
        });
    }

    /**
     * Hiển thị dialog chỉnh sửa hồ sơ (thay vì chỉ đổi mật khẩu)
     */
    private void showEditProfileDialog() {
        showEditProfileFullDialog();
    }

    /**
     * Hiển thị dialog xác nhận đăng xuất
     */
    private void showLogoutConfirmDialog() {
        new AlertDialog.Builder(getContext())
                .setTitle("Đăng xuất")
                .setMessage("Bạn có chắc chắn muốn đăng xuất?")
                .setPositiveButton("Đăng xuất", (dialog, which) -> {
                    authManager.logout();
                    navigateToAuth();
                })
                .setNegativeButton("Hủy", null)
                .show();
    }

    /**
     * Hiển thị dialog chỉnh sửa hồ sơ (chỉ tên và email)
     */
    private void showEditProfileFullDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());

        // Tạo layout cho dialog
        LinearLayout layout = new LinearLayout(getContext());
        layout.setOrientation(LinearLayout.VERTICAL);
        layout.setPadding(50, 40, 50, 40);

        // Username input
        TextView tvUsernameLabel = new TextView(getContext());
        tvUsernameLabel.setText("Tên hiển thị:");
        tvUsernameLabel.setTextSize(16);
        tvUsernameLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        tvUsernameLabel.setTextColor(getResources().getColor(android.R.color.black));
        layout.addView(tvUsernameLabel);

        EditText etUsername = new EditText(getContext());
        etUsername.setHint("Nhập tên hiển thị");
        etUsername.setText(currentUser != null ? currentUser.getUsername() : "");
        etUsername.setPadding(0, 20, 0, 20);
        layout.addView(etUsername);

        // Email input
        TextView tvEmailLabel = new TextView(getContext());
        tvEmailLabel.setText("Email:");
        tvEmailLabel.setTextSize(16);
        tvEmailLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        tvEmailLabel.setTextColor(getResources().getColor(android.R.color.black));
        tvEmailLabel.setPadding(0, 20, 0, 0);
        layout.addView(tvEmailLabel);

        EditText etEmail = new EditText(getContext());
        etEmail.setHint("Nhập địa chỉ email");
        etEmail.setText(currentUser != null ? currentUser.getEmail() : "");
        etEmail.setInputType(android.text.InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS);
        etEmail.setPadding(0, 20, 0, 20);
        layout.addView(etEmail);

        builder.setView(layout)
                .setTitle("✏️ Chỉnh sửa hồ sơ")
                .setPositiveButton("Lưu", (dialog, which) -> {
                    String username = etUsername.getText().toString().trim();
                    String email = etEmail.getText().toString().trim();

                    // Validate input
                    if (username.isEmpty() || email.isEmpty()) {
                        Toast.makeText(getContext(), "Vui lòng điền đầy đủ thông tin", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    // Validate email format
                    if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
                        Toast.makeText(getContext(), "Định dạng email không hợp lệ", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    // Cập nhật hồ sơ
                    updateUserProfileSimple(username, email);
                })
                .setNegativeButton("Hủy", null)
                .show();
    }

    /**
     * Cập nhật thông tin hồ sơ người dùng (chỉ tên và email)
     */
    private void updateUserProfileSimple(String username, String email) {
        if (currentUser == null) {
            Toast.makeText(getContext(), "Lỗi: Không tìm thấy thông tin người dùng", Toast.LENGTH_SHORT).show();
            return;
        }

        // Hiển thị loading
        Toast.makeText(getContext(), "Đang cập nhật hồ sơ...", Toast.LENGTH_SHORT).show();

        // Thực hiện cập nhật trong background thread
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            try {
                int userId = authManager.getCurrentUserId();

                // 1. Lấy thông tin user hiện tại
                UserEntity userEntity = todoViewModel.getRepository().getUserByIdSync(userId);
                if (userEntity == null) {
                    showErrorOnMainThread("Không tìm thấy thông tin người dùng");
                    return;
                }

                // 2. Kiểm tra username và email đã tồn tại chưa (nếu thay đổi)
                if (!username.equals(userEntity.getUsername())) {
                    int usernameExists = todoViewModel.getRepository().checkUsernameExists(username);
                    if (usernameExists > 0) {
                        showErrorOnMainThread("Tên hiển thị đã tồn tại");
                        return;
                    }
                }

                if (!email.equals(userEntity.getEmail())) {
                    int emailExists = todoViewModel.getRepository().checkEmailExists(email);
                    if (emailExists > 0) {
                        showErrorOnMainThread("Email đã được sử dụng");
                        return;
                    }
                }

                // 3. Cập nhật thông tin
                userEntity.setUsername(username);
                userEntity.setEmail(email);

                // 4. Lưu vào database
                todoViewModel.getRepository().updateUser(userEntity);

                // 5. Cập nhật AuthManager và UI trên main thread
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        // Cập nhật current user object
                        currentUser.setUsername(username);
                        currentUser.setEmail(email);

                        // Cập nhật AuthManager
                        authManager.updateCurrentUser(currentUser);

                        // Refresh UI
                        updateUserInfo();

                        // Hiển thị thông báo thành công
                        Toast.makeText(getContext(), "✅ Cập nhật hồ sơ thành công!", Toast.LENGTH_SHORT).show();
                    });
                }

            } catch (Exception e) {
                android.util.Log.e("ProfileFragment", "Error updating profile", e);
                showErrorOnMainThread("Lỗi cập nhật hồ sơ: " + e.getMessage());
            }
        });
    }

    /**
     * Hiển thị lỗi trên main thread
     */
    private void showErrorOnMainThread(String message) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                Toast.makeText(getContext(), message, Toast.LENGTH_LONG).show();
            });
        }
    }

    /**
     * Test thông báo nhắc nhở tasks
     */
    private void testNotificationReminder() {
        // Chạy ngay lập tức để test
        new Thread(() -> {
            try {
                android.util.Log.d("ProfileFragment", "Testing notification worker...");
                if (getContext() != null) {
                    com.example.myapplication.workers.NotificationWorker.checkAndSendNotifications(getContext());
                }

                // Chạy lại sau 10 giây
                Thread.sleep(10000);
                if (getContext() != null) {
                    android.util.Log.d("ProfileFragment", "Running notification worker again...");
                    com.example.myapplication.workers.NotificationWorker.checkAndSendNotifications(getContext());
                }
            } catch (InterruptedException e) {
                android.util.Log.e("ProfileFragment", "Test notification interrupted", e);
            }
        }).start();
    }

    /**
     * Chuyển về AuthActivity
     */
    private void navigateToAuth() {
        Intent intent = new Intent(getActivity(), AuthActivity.class);
        startActivity(intent);
        if (getActivity() != null) {
            getActivity().finish();
        }
    }
}

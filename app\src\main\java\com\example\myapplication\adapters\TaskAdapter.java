package com.example.myapplication.adapters;

import android.graphics.Paint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapplication.R;
import com.example.myapplication.models.Task;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

/**
 * TaskAdapter - Adapter cho RecyclerView hiển thị danh sách Task
 * Chức năng chính:
 * - Hiển thị thông tin task (title, description, deadline, category)
 * - <PERSON><PERSON> lý sự kiện click vào task và toggle completion
 * - Cập nhật UI dựa trên trạng thái hoàn thành của task
 * - Hiển thị progress của task steps
 */
public class TaskAdapter extends RecyclerView.Adapter<TaskAdapter.TaskViewHolder> {

    private List<Task> taskList; // Danh sách task để hiển thị
    private OnTaskClickListener onTaskClickListener; // Listener cho các sự kiện

    /**
     * Constructor của adapter
     * @param taskList Danh sách task ban đầu
     */
    public TaskAdapter(List<Task> taskList) {
        this.taskList = taskList;
    }

    /**
     * Interface để xử lý các sự kiện từ item
     */
    public interface OnTaskClickListener {
        void onTaskClick(Task task); // Sự kiện click vào task
        void onTaskToggle(Task task, boolean isCompleted); // Sự kiện toggle completion
    }

    /**
     * Thiết lập listener cho các sự kiện task
     * @param listener OnTaskClickListener để xử lý sự kiện
     */
    public void setOnTaskClickListener(OnTaskClickListener listener) {
        this.onTaskClickListener = listener;
    }

    /**
     * Tạo ViewHolder mới cho item
     * @param parent ViewGroup chứa item
     * @param viewType Loại view (không sử dụng)
     * @return TaskViewHolder mới được tạo
     */
    @NonNull
    @Override
    public TaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_task, parent, false);
        return new TaskViewHolder(view);
    }

    /**
     * Bind dữ liệu task vào ViewHolder
     * @param holder ViewHolder cần bind dữ liệu
     * @param position Vị trí của item trong list
     */
    @Override
    public void onBindViewHolder(@NonNull TaskViewHolder holder, int position) {
        Task task = taskList.get(position);

        // Hiển thị thông tin cơ bản của task
        holder.titleTextView.setText(task.getTitle());
        holder.descriptionTextView.setText(task.getDescription());

        // Xử lý hiển thị category (có thể null)
        if (task.getCategory() != null) {
            holder.categoryTextView.setText(task.getCategory().getName());
        } else {
            holder.categoryTextView.setText("Không có danh mục");
        }

        // Hiển thị mức độ ưu tiên
        holder.priorityTextView.setText(task.getPriority().getDisplayName());

        // Clear listener trước khi set checked để tránh trigger không mong muốn
        holder.checkBox.setOnCheckedChangeListener(null);
        // Debug log để kiểm tra trạng thái checkbox
        android.util.Log.d("TaskAdapter", "Setting checkbox for task: " + task.getTitle() + ", completed: " + task.isCompleted());
        holder.checkBox.setChecked(task.isCompleted());
        


        // Format and set start/end time
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("dd/MM HH:mm", Locale.getDefault());
        if (task.getStartTime() != null) {
            holder.startTimeTextView.setText("🕐 Bắt đầu: " + dateTimeFormat.format(task.getStartTime()));
        } else {
            holder.startTimeTextView.setText("🕐 Bắt đầu: --/-- --:--");
        }

        if (task.getEndTime() != null) {
            holder.endTimeTextView.setText("🕐 Kết thúc: " + dateTimeFormat.format(task.getEndTime()));
        } else {
            holder.endTimeTextView.setText("🕐 Kết thúc: --/-- --:--");
        }
        
        // Set priority color
        holder.priorityIndicator.setBackgroundColor(android.graphics.Color.parseColor(task.getPriority().getColor()));
        
        // Set category color
        holder.categoryTextView.setBackgroundColor(task.getCategory().getColorInt());
        
        // Strike through text if completed
        if (task.isCompleted()) {
            holder.titleTextView.setPaintFlags(holder.titleTextView.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
            holder.descriptionTextView.setPaintFlags(holder.descriptionTextView.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
            holder.itemView.setAlpha(0.6f);
        } else {
            holder.titleTextView.setPaintFlags(holder.titleTextView.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
            holder.descriptionTextView.setPaintFlags(holder.descriptionTextView.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
            holder.itemView.setAlpha(1.0f);
        }
        
        // Set overdue styling for start/end time if needed
        if (task.isOverdue()) {
            // Could style start/end time text with red color if overdue
            holder.startTimeTextView.setTextColor(android.graphics.Color.parseColor("#F44336"));
            holder.endTimeTextView.setTextColor(android.graphics.Color.parseColor("#F44336"));
        } else {
            holder.startTimeTextView.setTextColor(android.graphics.Color.parseColor("#888888"));
            holder.endTimeTextView.setTextColor(android.graphics.Color.parseColor("#888888"));
        }
        
        // Click listeners
        holder.itemView.setOnClickListener(v -> {
            if (onTaskClickListener != null) {
                onTaskClickListener.onTaskClick(task);
            }
        });
        
        // Set checkbox listener
        holder.checkBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // Only update if the state actually changed
            if (task.isCompleted() != isChecked) {
                if (onTaskClickListener != null) {
                    onTaskClickListener.onTaskToggle(task, isChecked);
                }
            }
        });
    }

    /**
     * Trả về số lượng item trong list
     * @return Số lượng task
     */
    @Override
    public int getItemCount() {
        return taskList.size();
    }

    /**
     * Cập nhật toàn bộ danh sách task
     * @param newTasks Danh sách task mới
     */
    public void updateTasks(List<Task> newTasks) {
        this.taskList = newTasks;
        notifyDataSetChanged(); // Refresh toàn bộ RecyclerView
    }

    /**
     * Cập nhật một task cụ thể trong danh sách
     * @param updatedTask Task đã được cập nhật
     */
    public void updateTask(Task updatedTask) {
        for (int i = 0; i < taskList.size(); i++) {
            if (taskList.get(i).getId().equals(updatedTask.getId())) {
                taskList.set(i, updatedTask);
                notifyItemChanged(i); // Chỉ refresh item cụ thể
                break;
            }
        }
    }

    /**
     * TaskViewHolder - ViewHolder cho task item
     * Chức năng: Giữ reference đến các view trong item layout
     */
    public static class TaskViewHolder extends RecyclerView.ViewHolder {
        TextView titleTextView, descriptionTextView, categoryTextView, priorityTextView; // Text views chính
        TextView startTimeTextView, endTimeTextView; // Text views thời gian
        CheckBox checkBox; // Checkbox hoàn thành
        View priorityIndicator; // Indicator màu ưu tiên

        /**
         * Constructor của ViewHolder
         * @param itemView View của item
         */
        public TaskViewHolder(@NonNull View itemView) {
            super(itemView);
            // Khởi tạo các view references
            titleTextView = itemView.findViewById(R.id.tv_task_title);
            descriptionTextView = itemView.findViewById(R.id.tv_task_description);
            categoryTextView = itemView.findViewById(R.id.tv_task_category);
            priorityTextView = itemView.findViewById(R.id.tv_task_priority);
            startTimeTextView = itemView.findViewById(R.id.tv_task_start_time);
            endTimeTextView = itemView.findViewById(R.id.tv_task_end_time);
            checkBox = itemView.findViewById(R.id.cb_task_completed);
            priorityIndicator = itemView.findViewById(R.id.view_priority_indicator);
        }
    }
}

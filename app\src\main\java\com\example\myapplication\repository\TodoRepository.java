package com.example.myapplication.repository;

import android.app.Application;
import androidx.lifecycle.LiveData;

import com.example.myapplication.database.TodoDatabase;
import com.example.myapplication.database.dao.CategoryDao;
import com.example.myapplication.database.dao.TaskDao;
import com.example.myapplication.database.dao.TaskStepDao;
import com.example.myapplication.database.dao.UserDao;
import com.example.myapplication.database.entities.CategoryEntity;
import com.example.myapplication.database.entities.TaskEntity;
import com.example.myapplication.database.entities.TaskStepEntity;
import com.example.myapplication.database.entities.UserEntity;
import com.example.myapplication.models.Task;

import java.util.Date;
import java.util.List;

/**
 * TodoRepository - Repository pattern để quản lý truy cập dữ liệu
 * Chức năng chính:
 * - Cung cấp interface thống nhất để truy cập dữ liệu từ Room database
 * - Quản lý các <PERSON> (Data Access Object)
 * - <PERSON><PERSON> cấp LiveData cho UI components
 * - <PERSON><PERSON> lý các thao tác CRUD cho Task, Category, User, TaskStep
 */
public class TodoRepository {

    // Các DAO để truy cập dữ liệu
    private TaskDao taskDao; // DAO cho Task operations
    private CategoryDao categoryDao; // DAO cho Category operations
    private TaskStepDao taskStepDao; // DAO cho TaskStep operations
    private UserDao userDao; // DAO cho User operations

    // LiveData cho UI
    private LiveData<List<TaskEntity>> allTasks; // LiveData cho tất cả task
    private LiveData<List<CategoryEntity>> allCategories; // LiveData cho tất cả category

    /**
     * Constructor của Repository
     * @param application Application context để lấy database instance
     */
    public TodoRepository(Application application) {
        TodoDatabase database = TodoDatabase.getDatabase(application);
        taskDao = database.taskDao();
        categoryDao = database.categoryDao();
        taskStepDao = database.taskStepDao();
        userDao = database.userDao();
        allTasks = taskDao.getAllTasks(); // Khởi tạo LiveData cho task
        allCategories = categoryDao.getAllCategories(); // Khởi tạo LiveData cho category
    }

    // Task operations
    public LiveData<List<TaskEntity>> getAllTasks() {
        return allTasks;
    }

    public LiveData<List<TaskEntity>> getTasksByUser(int userId) {
        return taskDao.getTasksByUser(userId);
    }

    public LiveData<TaskEntity> getTaskById(int taskId) {
        return taskDao.getTaskById(taskId);
    }

    public LiveData<List<TaskEntity>> getTasksByCategory(int categoryId) {
        return taskDao.getTasksByCategory(categoryId);
    }

    public LiveData<List<TaskEntity>> getTasksByDate(Date date) {
        return taskDao.getTasksByDate(date);
    }

    public LiveData<List<TaskEntity>> searchTasks(String query) {
        return taskDao.searchTasks(query);
    }

    public LiveData<List<TaskEntity>> getTodayTasks() {
        return taskDao.getTodayTasks();
    }

    public LiveData<List<TaskEntity>> getOverdueTasks() {
        return taskDao.getOverdueTasks(new Date());
    }

    public LiveData<List<TaskEntity>> getTasksByPriorityAndDate() {
        return taskDao.getTasksByPriorityAndDate();
    }

    public void insertTask(TaskEntity task) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            long taskId = taskDao.insertTask(task);
            // Update category task count
            updateCategoryTaskCount(task.getCategoryId());
        });
    }

    public long insertTaskAndGetId(TaskEntity task) {
        long taskId = taskDao.insertTask(task);
        // Update category task count
        updateCategoryTaskCount(task.getCategoryId());
        return taskId;
    }

    public void updateTask(TaskEntity task) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            taskDao.updateTask(task);
        });
    }

    public void updateTaskCompletion(int taskId, boolean isCompleted) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            taskDao.updateTaskCompletion(taskId, isCompleted, new Date());
        });
    }

    public void deleteTask(TaskEntity task) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            taskDao.deleteTask(task);
            // Update category task count
            updateCategoryTaskCount(task.getCategoryId());
        });
    }

    public void deleteTaskById(int taskId) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            taskDao.deleteTaskById(taskId);
        });
    }

    // Category operations
    public LiveData<List<CategoryEntity>> getAllCategories() {
        return allCategories;
    }

    public LiveData<List<CategoryEntity>> getCategoriesForUser(int userId) {
        return categoryDao.getCategoriesForUser(userId);
    }

    public LiveData<List<CategoryEntity>> getCategoriesWithTaskCount() {
        return categoryDao.getCategoriesWithTaskCount();
    }

    public LiveData<CategoryEntity> getCategoryById(int categoryId) {
        return categoryDao.getCategoryById(categoryId);
    }

    public void insertCategory(CategoryEntity category) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            categoryDao.insertCategory(category);
        });
    }

    public void updateCategory(CategoryEntity category) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            categoryDao.updateCategory(category);
        });
    }

    public void deleteCategory(CategoryEntity category) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            categoryDao.deleteCategory(category);
        });
    }

    // Statistics operations
    public LiveData<Integer> getTotalTaskCount() {
        return taskDao.getTotalTaskCount();
    }

    public LiveData<Integer> getCompletedTaskCount() {
        return taskDao.getCompletedTaskCount();
    }

    public LiveData<Integer> getPendingTaskCount() {
        return taskDao.getPendingTaskCount();
    }

    public LiveData<Integer> getOverdueTaskCount() {
        return taskDao.getOverdueTaskCount(new Date());
    }

    public LiveData<Integer> getTaskCountByCategory(int categoryId) {
        return taskDao.getTaskCountByCategory(categoryId);
    }

    // Helper methods
    private void updateCategoryTaskCount(int categoryId) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            // This would require a more complex query to count tasks by category
            // For now, we'll use the getCategoriesWithTaskCount() method in the UI
        });
    }

    // TaskStep operations
    public LiveData<List<TaskStepEntity>> getStepsByTaskId(int taskId) {
        return taskStepDao.getStepsByTaskId(taskId);
    }

    public void insertTaskStep(TaskStepEntity step) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            taskStepDao.insert(step);
        });
    }

    public long insertTaskStepSync(TaskStepEntity step) {
        return taskStepDao.insertSync(step);
    }

    public void updateTaskStepSync(TaskStepEntity step) {
        taskStepDao.update(step);
    }

    public void deleteTaskStepSync(TaskStepEntity step) {
        taskStepDao.delete(step);
    }

    public void updateTaskSync(TaskEntity task) {
        taskDao.updateTask(task);
    }

    public long insertCategorySync(CategoryEntity category) {
        return categoryDao.insertCategory(category);
    }

    public void insertTaskSteps(List<TaskStepEntity> steps) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            taskStepDao.insertAll(steps);
        });
    }

    public void updateTaskStep(TaskStepEntity step) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            taskStepDao.update(step);
        });
    }

    public void deleteTaskStep(TaskStepEntity step) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            taskStepDao.delete(step);
        });
    }

    public void deleteStepsByTaskId(int taskId) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            taskStepDao.deleteStepsByTaskId(taskId);
        });
    }

    // Utility method to convert Task to TaskEntity
    public TaskEntity convertTaskToEntity(Task task, int categoryId) {
        TaskEntity entity = TaskEntity.fromTask(task);
        entity.setCategoryId(categoryId);
        return entity;
    }

    // User operations
    public LiveData<UserEntity> getUserById(int userId) {
        return userDao.getUserById(userId);
    }

    public UserEntity getUserByIdSync(int userId) {
        return userDao.getUserByIdSync(userId);
    }

    public UserEntity getUserByUsername(String username) {
        return userDao.getUserByUsername(username);
    }

    public UserEntity authenticateUser(String username, String passwordHash) {
        return userDao.authenticateUser(username, passwordHash);
    }

    public long insertUser(UserEntity user) {
        return userDao.insertUser(user);
    }

    public void updateUser(UserEntity user) {
        userDao.updateUser(user);
    }

    public int checkUsernameExists(String username) {
        return userDao.checkUsernameExists(username);
    }

    public int checkEmailExists(String email) {
        return userDao.checkEmailExists(email);
    }
}

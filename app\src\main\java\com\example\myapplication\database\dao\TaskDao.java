package com.example.myapplication.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.example.myapplication.database.entities.TaskEntity;
import com.example.myapplication.models.Task;

import java.util.Date;
import java.util.List;

@Dao
public interface TaskDao {

    // Insert operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertTask(TaskEntity task);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertTasks(List<TaskEntity> tasks);

    // Update operations
    @Update
    void updateTask(TaskEntity task);

    @Query("UPDATE tasks SET is_completed = :isCompleted, updated_at = :updatedAt WHERE id = :taskId")
    void updateTaskCompletion(int taskId, boolean isCompleted, Date updatedAt);

    // Delete operations
    @Delete
    void deleteTask(TaskEntity task);

    @Query("DELETE FROM tasks WHERE id = :taskId")
    void deleteTaskById(int taskId);

    @Query("DELETE FROM tasks")
    void deleteAllTasks();

    // Select operations
    @Query("SELECT * FROM tasks ORDER BY created_at DESC")
    LiveData<List<TaskEntity>> getAllTasks();

    @Query("SELECT * FROM tasks WHERE user_id = :userId ORDER BY created_at DESC")
    LiveData<List<TaskEntity>> getTasksByUser(int userId);

    @Query("SELECT * FROM tasks WHERE user_id = :userId AND is_completed = 0 ORDER BY due_date ASC")
    List<TaskEntity> getIncompleteTasksByUserSync(int userId);

    @Query("SELECT COUNT(*) FROM tasks WHERE user_id = :userId AND is_completed = 1 AND updated_at >= :startDate AND updated_at < :endDate")
    int getCompletedTasksCountByUserAndDateRange(int userId, java.util.Date startDate, java.util.Date endDate);

    @Query("SELECT * FROM tasks WHERE id = :taskId")
    LiveData<TaskEntity> getTaskById(int taskId);

    @Query("SELECT * FROM tasks WHERE category_id = :categoryId ORDER BY created_at DESC")
    LiveData<List<TaskEntity>> getTasksByCategory(int categoryId);

    @Query("SELECT * FROM tasks WHERE is_completed = :isCompleted ORDER BY created_at DESC")
    LiveData<List<TaskEntity>> getTasksByCompletion(boolean isCompleted);

    @Query("SELECT * FROM tasks WHERE priority = :priority ORDER BY created_at DESC")
    LiveData<List<TaskEntity>> getTasksByPriority(Task.Priority priority);

    @Query("SELECT * FROM tasks WHERE due_date BETWEEN :startDate AND :endDate ORDER BY due_date ASC")
    LiveData<List<TaskEntity>> getTasksByDateRange(Date startDate, Date endDate);

    @Query("SELECT * FROM tasks WHERE DATE(due_date/1000, 'unixepoch') = DATE(:date/1000, 'unixepoch') ORDER BY priority DESC")
    LiveData<List<TaskEntity>> getTasksByDate(Date date);

    @Query("SELECT * FROM tasks WHERE title LIKE '%' || :searchQuery || '%' OR description LIKE '%' || :searchQuery || '%' ORDER BY created_at DESC")
    LiveData<List<TaskEntity>> searchTasks(String searchQuery);

    // Count operations
    @Query("SELECT COUNT(*) FROM tasks")
    LiveData<Integer> getTotalTaskCount();

    @Query("SELECT COUNT(*) FROM tasks WHERE is_completed = 1")
    LiveData<Integer> getCompletedTaskCount();

    @Query("SELECT COUNT(*) FROM tasks WHERE is_completed = 0")
    LiveData<Integer> getPendingTaskCount();

    @Query("SELECT COUNT(*) FROM tasks WHERE category_id = :categoryId")
    LiveData<Integer> getTaskCountByCategory(int categoryId);

    @Query("SELECT COUNT(*) FROM tasks WHERE due_date < :currentDate AND is_completed = 0")
    LiveData<Integer> getOverdueTaskCount(Date currentDate);

    // Advanced queries
    @Query("SELECT * FROM tasks WHERE is_completed = 0 AND due_date < :currentDate ORDER BY due_date ASC")
    LiveData<List<TaskEntity>> getOverdueTasks(Date currentDate);

    @Query("SELECT * FROM tasks WHERE is_completed = 0 AND DATE(due_date/1000, 'unixepoch') = DATE('now') ORDER BY priority DESC")
    LiveData<List<TaskEntity>> getTodayTasks();

    @Query("SELECT * FROM tasks WHERE is_completed = 0 ORDER BY CASE priority WHEN 'HIGH' THEN 1 WHEN 'MEDIUM' THEN 2 WHEN 'LOW' THEN 3 END, due_date ASC")
    LiveData<List<TaskEntity>> getTasksByPriorityAndDate();
}

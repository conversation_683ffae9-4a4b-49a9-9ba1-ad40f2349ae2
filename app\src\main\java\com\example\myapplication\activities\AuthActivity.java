package com.example.myapplication.activities;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.example.myapplication.MainActivity;
import com.example.myapplication.R;
import com.example.myapplication.auth.AuthManager;
import com.example.myapplication.fragments.auth.LoginFragment;
import com.example.myapplication.fragments.auth.RegisterFragment;
import com.example.myapplication.models.User;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

/**
 * AuthActivity - Activity xử lý xác thực người dùng
 * Chức năng: Quản lý màn hình đăng nhập và đăng ký
 * Implement các interface để nhận callback từ LoginFragment và RegisterFragment
 */
public class AuthActivity extends AppCompatActivity implements LoginFragment.LoginListener, RegisterFragment.RegisterListener {

    private TabLayout tabLayout; // Tab layout cho đăng nhập/đăng ký
    private ViewPager2 viewPager; // ViewPager để chứa các fragment
    private AuthPagerAdapter pagerAdapter; // Adapter quản lý các fragment
    private AuthManager authManager; // Quản lý xác thực

    /**
     * Phương thức onCreate - Được gọi khi Activity được tạo
     * Chức năng:
     * - Kiểm tra trạng thái đăng nhập
     * - Thiết lập ViewPager và TabLayout cho đăng nhập/đăng ký
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_auth);

        authManager = AuthManager.getInstance(this);

        // Kiểm tra nếu người dùng đã đăng nhập thì chuyển về MainActivity
        if (authManager.isLoggedIn()) {
            navigateToMain();
            return;
        }

        // Khởi tạo các view và thiết lập ViewPager
        initViews();
        setupViewPager();
    }

    /**
     * Khởi tạo các view components
     * Chức năng: Liên kết các view từ layout
     */
    private void initViews() {
        tabLayout = findViewById(R.id.tab_layout);
        viewPager = findViewById(R.id.view_pager);
    }

    /**
     * Thiết lập ViewPager và TabLayout
     * Chức năng: Tạo adapter và liên kết tab với ViewPager
     */
    private void setupViewPager() {
        pagerAdapter = new AuthPagerAdapter(this);
        viewPager.setAdapter(pagerAdapter);

        // Liên kết TabLayout với ViewPager và đặt tên cho các tab
        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            switch (position) {
                case 0:
                    tab.setText("Đăng nhập"); // Tab đầu tiên
                    break;
                case 1:
                    tab.setText("Đăng ký"); // Tab thứ hai
                    break;
            }
        }).attach();
    }

    /**
     * Callback khi đăng nhập thành công
     * Được gọi từ LoginFragment thông qua interface
     * @param user Thông tin người dùng đã đăng nhập
     */
    @Override
    public void onLoginSuccess(User user) {
        Toast.makeText(this, "Chào mừng " + user.getDisplayName() + "!", Toast.LENGTH_SHORT).show();
        navigateToMain(); // Chuyển đến MainActivity
    }

    /**
     * Callback khi đăng nhập thất bại
     * Được gọi từ LoginFragment thông qua interface
     * @param error Thông báo lỗi
     */
    @Override
    public void onLoginError(String error) {
        Toast.makeText(this, error, Toast.LENGTH_LONG).show();
    }

    /**
     * Callback khi đăng ký thành công
     * Được gọi từ RegisterFragment thông qua interface
     * @param user Thông tin người dùng đã đăng ký
     */
    @Override
    public void onRegisterSuccess(User user) {
        // Đăng xuất người dùng sau khi đăng ký để buộc phải đăng nhập lại
        authManager.logout();

        Toast.makeText(this, "Đăng ký thành công! Vui lòng đăng nhập để tiếp tục.", Toast.LENGTH_LONG).show();

        // Chuyển về tab đăng nhập
        viewPager.setCurrentItem(0, true);
    }

    /**
     * Callback khi đăng ký thất bại
     * Được gọi từ RegisterFragment thông qua interface
     * @param error Thông báo lỗi
     */
    @Override
    public void onRegisterError(String error) {
        Toast.makeText(this, error, Toast.LENGTH_LONG).show();
    }

    /**
     * Điều hướng đến MainActivity
     * Chức năng: Chuyển đến màn hình chính và đóng AuthActivity
     */
    private void navigateToMain() {
        Intent intent = new Intent(this, MainActivity.class);
        // Clear task stack để người dùng không thể quay lại AuthActivity
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish(); // Đóng AuthActivity
    }

    /**
     * AuthPagerAdapter - Adapter quản lý các fragment trong ViewPager
     * Chức năng: Tạo và quản lý LoginFragment và RegisterFragment
     */
    private static class AuthPagerAdapter extends FragmentStateAdapter {

        /**
         * Constructor của adapter
         * @param fragmentActivity Activity chứa ViewPager
         */
        public AuthPagerAdapter(FragmentActivity fragmentActivity) {
            super(fragmentActivity);
        }

        /**
         * Tạo fragment tương ứng với vị trí
         * @param position Vị trí của tab (0: Login, 1: Register)
         * @return Fragment tương ứng
         */
        @Override
        public Fragment createFragment(int position) {
            switch (position) {
                case 0:
                    return new LoginFragment(); // Tab đăng nhập
                case 1:
                    return new RegisterFragment(); // Tab đăng ký
                default:
                    return new LoginFragment(); // Mặc định là đăng nhập
            }
        }

        /**
         * Trả về số lượng tab
         * @return Số lượng fragment (2: Login và Register)
         */
        @Override
        public int getItemCount() {
            return 2;
        }
    }
}

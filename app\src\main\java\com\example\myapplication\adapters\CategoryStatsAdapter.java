package com.example.myapplication.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapplication.R;
import com.example.myapplication.database.entities.CategoryEntity;

import java.util.List;
import java.util.Map;

/**
 * Adapter hiển thị thống kê theo danh mục
 */
public class CategoryStatsAdapter extends RecyclerView.Adapter<CategoryStatsAdapter.CategoryStatsViewHolder> {

    private List<CategoryEntity> categories;
    private List<CategoryEntity> filteredCategories; // Chỉ categories có tasks
    private Map<Integer, Integer> categoryTaskCount;
    private Map<Integer, Integer> categoryCompletedCount;

    public CategoryStatsAdapter(List<CategoryEntity> categories,
                               Map<Integer, Integer> categoryTaskCount,
                               Map<Integer, Integer> categoryCompletedCount) {
        this.categories = categories;
        this.categoryTaskCount = categoryTaskCount;
        this.categoryCompletedCount = categoryCompletedCount;
        filterCategories();
    }

    @NonNull
    @Override
    public CategoryStatsViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_category_stats, parent, false);
        return new CategoryStatsViewHolder(view);
    }

    /**
     * Filter categories để chỉ hiển thị những category có tasks
     */
    private void filterCategories() {
        filteredCategories = new java.util.ArrayList<>();
        if (categories != null && categoryTaskCount != null) {
            android.util.Log.d("CategoryStatsAdapter", "Filtering " + categories.size() + " categories");
            for (CategoryEntity category : categories) {
                int totalTasks = categoryTaskCount.getOrDefault(category.getId(), 0);
                android.util.Log.d("CategoryStatsAdapter", "Category: " + category.getName() + ", Tasks: " + totalTasks);
                if (totalTasks > 0) {
                    filteredCategories.add(category);
                }
            }
            android.util.Log.d("CategoryStatsAdapter", "Filtered to " + filteredCategories.size() + " categories with tasks");
        }
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryStatsViewHolder holder, int position) {
        CategoryEntity category = filteredCategories.get(position);
        int categoryId = category.getId();

        // Lấy số liệu thống kê
        int totalTasks = categoryTaskCount.getOrDefault(categoryId, 0);
        int completedTasks = categoryCompletedCount.getOrDefault(categoryId, 0);

        holder.tvCategoryName.setText(category.getIcon() + " " + category.getName());
        holder.tvTaskCount.setText(completedTasks + "/" + totalTasks + " nhiệm vụ");

        // Tính phần trăm hoàn thành
        int percentage = totalTasks > 0 ? (completedTasks * 100) / totalTasks : 0;
        holder.progressBar.setProgress(percentage);
        holder.tvPercentage.setText(percentage + "%");

        // Đặt màu cho progress bar theo màu category
        try {
            int color = android.graphics.Color.parseColor(category.getColor());
            holder.progressBar.setProgressTintList(android.content.res.ColorStateList.valueOf(color));
        } catch (Exception e) {
            // Sử dụng màu mặc định nếu parse color thất bại
            holder.progressBar.setProgressTintList(android.content.res.ColorStateList.valueOf(0xFF2196F3));
        }
    }

    @Override
    public int getItemCount() {
        return filteredCategories != null ? filteredCategories.size() : 0;
    }

    /**
     * Cập nhật dữ liệu thống kê
     */
    public void updateStats(List<CategoryEntity> categories,
                           Map<Integer, Integer> categoryTaskCount,
                           Map<Integer, Integer> categoryCompletedCount) {
        this.categories = categories;
        this.categoryTaskCount = categoryTaskCount;
        this.categoryCompletedCount = categoryCompletedCount;
        filterCategories(); // Filter lại sau khi update
        notifyDataSetChanged();
    }

    public static class CategoryStatsViewHolder extends RecyclerView.ViewHolder {
        TextView tvCategoryName, tvTaskCount, tvPercentage;
        ProgressBar progressBar;

        public CategoryStatsViewHolder(@NonNull View itemView) {
            super(itemView);
            tvCategoryName = itemView.findViewById(R.id.tv_category_name);
            tvTaskCount = itemView.findViewById(R.id.tv_task_count);
            tvPercentage = itemView.findViewById(R.id.tv_percentage);
            progressBar = itemView.findViewById(R.id.progress_bar);
        }
    }
}

package com.example.myapplication;

import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.example.myapplication.activities.AuthActivity;
import com.example.myapplication.auth.AuthManager;
import com.example.myapplication.databinding.ActivityMainBinding;
import com.example.myapplication.fragments.TaskListFragment;
import com.example.myapplication.fragments.CalendarFragment;
import com.example.myapplication.fragments.ProfileFragment;

/**
 * MainActivity - Activity chính của ứng dụng
 * Chức năng: Quản lý navigation giữa các fragment chính (Tasks, Calendar, Profile)
 * Kiểm tra trạng thái đăng nhập và điều hướng người dùng
 */
public class MainActivity extends AppCompatActivity {

    private ActivityMainBinding binding; // View binding để truy cập các view
    private AuthManager authManager; // Quản lý xác thực người dùng

    /**
     * <PERSON>ương thức onCreate - Được gọi khi Activity được tạo
     * Chức năng:
     * - Kiểm tra trạng thái đăng nhập
     * - Thiết lập giao diện và navigation
     * - Load fragment mặc định
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Kiểm tra xác thực trước khi hiển thị giao diện chính
        authManager = AuthManager.getInstance(this);
        if (!authManager.isLoggedIn()) {
            navigateToAuth(); // Chuyển đến màn hình đăng nhập nếu chưa đăng nhập
            return;
        }

        // Thiết lập view binding và layout
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Thiết lập bottom navigation
        setupBottomNavigation();

        // Load fragment mặc định (TaskListFragment) khi lần đầu mở app
        if (savedInstanceState == null) {
            loadFragment(new TaskListFragment());
        }
    }

    /**
     * Thiết lập Bottom Navigation
     * Chức năng: Xử lý sự kiện click vào các tab và chuyển đổi fragment tương ứng
     */
    private void setupBottomNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener(item -> {
            Fragment selectedFragment = null;
            int itemId = item.getItemId();

            // Xác định fragment nào sẽ được hiển thị dựa trên tab được chọn
            if (itemId == R.id.nav_tasks) {
                selectedFragment = new TaskListFragment(); // Tab Nhiệm vụ
            } else if (itemId == R.id.nav_calendar) {
                selectedFragment = new CalendarFragment(); // Tab Lịch
            } else if (itemId == R.id.nav_profile) {
                selectedFragment = new ProfileFragment(); // Tab Hồ sơ
            }

            // Load fragment được chọn
            if (selectedFragment != null) {
                loadFragment(selectedFragment);
                return true; // Xử lý thành công
            }
            return false; // Không xử lý được
        });
    }

    /**
     * Load fragment vào container
     * Chức năng: Thay thế fragment hiện tại bằng fragment mới
     * @param fragment Fragment cần hiển thị
     */
    private void loadFragment(Fragment fragment) {
        getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.fragment_container, fragment) // Thay thế fragment trong container
                .commit(); // Thực hiện transaction
    }

    /**
     * Điều hướng đến màn hình xác thực
     * Chức năng: Chuyển đến AuthActivity và đóng MainActivity
     */
    private void navigateToAuth() {
        Intent intent = new Intent(this, AuthActivity.class);
        // Clear task stack để người dùng không thể quay lại MainActivity khi chưa đăng nhập
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish(); // Đóng MainActivity
    }
}
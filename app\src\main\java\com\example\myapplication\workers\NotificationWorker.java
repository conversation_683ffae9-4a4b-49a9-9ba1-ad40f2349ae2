package com.example.myapplication.workers;

import android.content.Context;

import com.example.myapplication.auth.AuthManager;
import com.example.myapplication.database.TodoDatabase;
import com.example.myapplication.database.entities.TaskEntity;
import com.example.myapplication.utils.NotificationHelper;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Helper để gửi thông báo nhắc nhở
 */
public class NotificationWorker {

    /**
     * Kiểm tra và gửi thông báo nhắc nhở cho tasks
     */
    public static void checkAndSendNotifications(Context context) {
        try {
            NotificationHelper notificationHelper = new NotificationHelper(context);

            // Kiểm tra xem thông báo có được bật không
            if (!notificationHelper.isNotificationsEnabled()) {
                return;
            }

            AuthManager authManager = AuthManager.getInstance(context);
            if (!authManager.isLoggedIn()) {
                return;
            }

            int currentUserId = authManager.getCurrentUserId();
            TodoDatabase database = TodoDatabase.getDatabase(context);

            // Lấy tasks chưa hoàn thành của user hiện tại
            List<TaskEntity> incompleteTasks = database.taskDao().getIncompleteTasksByUserSync(currentUserId);

            if (incompleteTasks.isEmpty()) {
                // Gửi thông báo chúc mừng nếu không có task nào
                notificationHelper.showDailySummaryNotification(0, 0);
                return;
            }

            // Kiểm tra tasks cần thông báo (trước 1 tiếng bắt đầu hoặc kết thúc)
            Date now = new Date();
            Calendar cal = Calendar.getInstance();
            cal.setTime(now);
            cal.add(Calendar.HOUR_OF_DAY, 1); // Thời điểm 1 tiếng sau
            Date oneHourLater = cal.getTime();

            boolean sentNotification = false;

            for (TaskEntity task : incompleteTasks) {
                android.util.Log.d("NotificationWorker", "Checking task: " + task.getTitle() +
                    ", StartTime: " + task.getStartTime() +
                    ", EndTime: " + task.getEndTime() +
                    ", DueDate: " + task.getDueDate());

                // Kiểm tra thông báo trước 1 tiếng bắt đầu
                if (task.getStartTime() != null) {
                    long timeDiffStart = task.getStartTime().getTime() - now.getTime();
                    long oneHourInMillis = 60 * 60 * 1000; // 1 tiếng = 60 phút * 60 giây * 1000 ms

                    android.util.Log.d("NotificationWorker", "Start time diff: " + (timeDiffStart / (60 * 1000)) + " minutes");

                    // Nếu còn khoảng 50-70 phút nữa sẽ bắt đầu (mở rộng khoảng thời gian)
                    if (timeDiffStart > (50 * 60 * 1000) && timeDiffStart <= (70 * 60 * 1000)) {
                        notificationHelper.showTaskStartReminder(task.getTitle(), task.getDescription(), task.getId());
                        sentNotification = true;
                        android.util.Log.d("NotificationWorker", "Sent start reminder for task: " + task.getTitle());
                    }

                    // Thêm thông báo test cho tasks trong vòng 2 tiếng (để test dễ hơn)
                    if (timeDiffStart > 0 && timeDiffStart <= (120 * 60 * 1000)) {
                        android.util.Log.d("NotificationWorker", "Task " + task.getTitle() + " will start in " + (timeDiffStart / (60 * 1000)) + " minutes");
                        // Gửi thông báo test
                        notificationHelper.showTaskReminder("Sắp có nhiệm vụ",
                            "Nhiệm vụ '" + task.getTitle() + "' sẽ bắt đầu trong " + (timeDiffStart / (60 * 1000)) + " phút",
                            task.getId());
                        sentNotification = true;
                    }
                }

                // Kiểm tra thông báo trước 1 tiếng kết thúc
                if (task.getEndTime() != null) {
                    long timeDiffEnd = task.getEndTime().getTime() - now.getTime();

                    android.util.Log.d("NotificationWorker", "End time diff: " + (timeDiffEnd / (60 * 1000)) + " minutes");

                    // Nếu còn khoảng 50-70 phút nữa sẽ kết thúc
                    if (timeDiffEnd > (50 * 60 * 1000) && timeDiffEnd <= (70 * 60 * 1000)) {
                        notificationHelper.showTaskEndReminder(task.getTitle(), task.getDescription(), task.getId());
                        sentNotification = true;
                        android.util.Log.d("NotificationWorker", "Sent end reminder for task: " + task.getTitle());
                    }
                }

                // Kiểm tra tasks quá hạn
                if (task.getDueDate() != null && task.getDueDate().before(now)) {
                    notificationHelper.showOverdueTaskReminder(task.getTitle(), task.getDescription(), task.getId());
                    sentNotification = true;
                    android.util.Log.d("NotificationWorker", "Sent overdue reminder for task: " + task.getTitle());
                }
            }

            // Nếu không có thông báo nào được gửi, gửi thông báo tổng kết (chỉ 1 lần/ngày)
            if (!sentNotification && shouldSendDailySummary()) {
                int totalTasks = incompleteTasks.size();
                int completedToday = getCompletedTasksToday(database, currentUserId);
                notificationHelper.showDailySummaryNotification(totalTasks, completedToday);
                markDailySummarySent(context);
            }

        } catch (Exception e) {
            android.util.Log.e("NotificationWorker", "Error in notification worker", e);
        }
    }

    /**
     * Kiểm tra xem có nên gửi thông báo tổng kết hàng ngày không
     */
    private static boolean shouldSendDailySummary() {
        // Chỉ gửi 1 lần vào buổi sáng (8-9h) hoặc buổi tối (18-19h)
        Calendar cal = Calendar.getInstance();
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        return (hour >= 8 && hour <= 9) || (hour >= 18 && hour <= 19);
    }

    /**
     * Lấy số tasks đã hoàn thành hôm nay
     */
    private static int getCompletedTasksToday(TodoDatabase database, int userId) {
        try {
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            Date startOfDay = cal.getTime();

            cal.add(Calendar.DAY_OF_MONTH, 1);
            Date startOfNextDay = cal.getTime();

            // Giả sử có method này trong TaskDao (sẽ thêm sau)
            return database.taskDao().getCompletedTasksCountByUserAndDateRange(userId, startOfDay, startOfNextDay);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * Đánh dấu đã gửi thông báo tổng kết hôm nay
     */
    private static void markDailySummarySent(Context context) {
        android.content.SharedPreferences prefs = context.getSharedPreferences("notification_prefs", Context.MODE_PRIVATE);
        String today = java.text.DateFormat.getDateInstance().format(new Date());
        prefs.edit().putString("last_daily_summary", today).apply();
    }
}

package com.example.myapplication.database.converters;

import androidx.room.TypeConverter;
import java.util.Date;

/**
 * DateConverter - TypeConverter cho Room database
 * Chức năng chính:
 * - Chuyển đổi giữa Date object và Long timestamp
 * - <PERSON> phép Room lưu trữ Date trong database
 * - <PERSON><PERSON> lý null values an toàn
 * - Hỗ trợ serialization/deserialization Date
 */
public class DateConverter {

    /**
     * Chuyển đổi từ Long timestamp thành Date object
     * @param value Long timestamp (có thể null)
     * @return Date object hoặc null
     */
    @TypeConverter
    public static Date fromTimestamp(Long value) {
        return value == null ? null : new Date(value);
    }

    /**
     * Chuyển đổi từ Date object thành Long timestamp
     * @param date Date object (có thể null)
     * @return Long timestamp hoặc null
     */
    @TypeConverter
    public static Long dateToTimestamp(Date date) {
        return date == null ? null : date.getTime();
    }
}

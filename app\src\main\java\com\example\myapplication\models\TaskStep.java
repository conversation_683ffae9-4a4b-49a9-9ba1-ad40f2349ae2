package com.example.myapplication.models;

import java.util.Date;

/**
 * TaskStep - Model class đại diện cho một bước con trong task
 * Chức năng chính:
 * - L<PERSON>u trữ thông tin chi tiết của từng bước
 * - <PERSON><PERSON><PERSON>n lý trạng thái hoàn thành và thời gian
 * - Hỗ trợ sắp xếp thứ tự các bước
 * - Tính toán progress tổng thể của task
 */
public class TaskStep {
    private String id;              // ID duy nhất của step
    private String title;           // Tiêu đề của bước
    private String description;     // Mô tả chi tiết bước
    private boolean completed;      // Trạng thái hoàn thành
    private Date createdAt;         // Thời gian tạo
    private Date completedAt;       // Thời gian hoàn thành
    private int order;              // Thứ tự sắp xếp trong task

    /**
     * Constructor mặc định
     * Khởi tạo với thời gian hiện tại và trạng thái chưa hoàn thành
     */
    public TaskStep() {
        this.createdAt = new Date();
        this.completed = false;
    }

    /**
     * Constructor với thông tin cơ bản
     * @param id ID của step
     * @param title Tiêu đề bước
     * @param description Mô tả bước
     */
    public TaskStep(String id, String title, String description) {
        this();
        this.id = id;
        this.title = title;
        this.description = description;
    }

    /**
     * Constructor với thứ tự sắp xếp
     * @param id ID của step
     * @param title Tiêu đề bước
     * @param description Mô tả bước
     * @param order Thứ tự sắp xếp
     */
    public TaskStep(String id, String title, String description, int order) {
        this(id, title, description);
        this.order = order;
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public boolean isCompleted() { return completed; }
    public void setCompleted(boolean completed) { 
        this.completed = completed;
        if (completed && completedAt == null) {
            this.completedAt = new Date();
        } else if (!completed) {
            this.completedAt = null;
        }
    }

    public Date getCreatedAt() { return createdAt; }
    public void setCreatedAt(Date createdAt) { this.createdAt = createdAt; }

    public Date getCompletedAt() { return completedAt; }
    public void setCompletedAt(Date completedAt) { this.completedAt = completedAt; }

    public int getOrder() { return order; }
    public void setOrder(int order) { this.order = order; }

    // Helper methods
    public String getStatusText() {
        return completed ? "Hoàn thành" : "Chưa hoàn thành";
    }

    public int getStatusColor() {
        return completed ? 
            android.graphics.Color.parseColor("#4CAF50") : 
            android.graphics.Color.parseColor("#757575");
    }

    @Override
    public String toString() {
        return title;
    }
}

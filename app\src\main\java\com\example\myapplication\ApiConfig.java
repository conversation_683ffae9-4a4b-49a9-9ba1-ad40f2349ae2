package com.example.myapplication;

public final class ApiConfig {

    private ApiConfig() {
        // Private constructor to prevent instantiation
    }
    
    // Dùng 10.0.2.2 cho máy ảo Android. Thay 3000 bằng port của server Node.js
    public static final String BASE_URL = "http://10.0.2.2:3000/api/";
    
    // Endpoints
    public static final String TASKS_ENDPOINT = "tasks";
    public static final String USERS_ENDPOINT = "users";
    public static final String AUTH_ENDPOINT = "auth";
    
    // Headers (nếu cần authentication)
    public static final String AUTH_HEADER = "Authorization";
    public static final String CONTENT_TYPE = "application/json";
    
    // Timeouts
    public static final int CONNECT_TIMEOUT = 10000; // 10 seconds
    public static final int READ_TIMEOUT = 10000; // 10 seconds
    
    // API Keys (nếu cần)
    public static final String API_KEY = "your-api-key-here";
    
    // Example API URLs:
    // GET /api/tasks - <PERSON><PERSON><PERSON> danh sách tasks
    // POST /api/tasks - Tạo task mới
    // PUT /api/tasks/{id} - Cập nhật task
    // DELETE /api/tasks/{id} - Xóa task
    
    /*
     * Ví dụ JSON format cho Task API:
     * 
     * GET /api/tasks Response:
     * [
     *   {
     *     "id": "1",
     *     "title": "Hoàn thành báo cáo",
     *     "category": "Công việc",
     *     "description": "Viết báo cáo tháng 12",
     *     "deadline": "28/12/2024",
     *     "status": "Đang thực hiện"
     *   }
     * ]
     * 
     * POST /api/tasks Request:
     * {
     *   "title": "Task mới",
     *   "category": "Cá nhân",
     *   "description": "Mô tả task",
     *   "deadline": "30/12/2024",
     *   "status": "Chưa bắt đầu"
     * }
     */
}

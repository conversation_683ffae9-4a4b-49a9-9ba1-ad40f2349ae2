package com.example.myapplication.fragments.auth;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.myapplication.R;
import com.example.myapplication.auth.AuthManager;
import com.example.myapplication.models.User;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;

/**
 * LoginFragment - Fragment xử lý đăng nhập
 * Chức năng: Hiển thị form đăng nhập và xử lý logic đăng nhập
 */
public class LoginFragment extends Fragment {

    /**
     * Interface để giao tiếp với Activity
     * Activity implement interface này để nhận callback từ Fragment
     */
    public interface LoginListener {
        void onLoginSuccess(User user); // Callback khi đăng nhập thành công
        void onLoginError(String error); // Callback khi đăng nhập thất bại
    }

    // Các view components
    private TextInputEditText etUsername, etPassword; // Input fields
    private CheckBox cbRememberMe; // Checkbox ghi nhớ đăng nhập
    private MaterialButton btnLogin; // Nút đăng nhập
    private TextView tvForgotPassword; // Text quên mật khẩu
    private ProgressBar progressBar; // Loading indicator

    private LoginListener loginListener; // Listener để giao tiếp với Activity
    private AuthManager authManager; // Quản lý xác thực

    /**
     * Được gọi khi Fragment được attach vào Activity
     * Chức năng: Thiết lập listener và AuthManager
     */
    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        // Kiểm tra Activity có implement LoginListener không
        if (context instanceof LoginListener) {
            loginListener = (LoginListener) context;
        }
        authManager = AuthManager.getInstance(context);
    }

    /**
     * Tạo view cho Fragment
     * @return View của fragment
     */
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_login, container, false);
    }

    /**
     * Được gọi sau khi view được tạo
     * Chức năng: Khởi tạo các view và thiết lập sự kiện
     */
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view); // Khởi tạo các view
        setupClickListeners(); // Thiết lập sự kiện click
    }

    /**
     * Khởi tạo các view components
     * Chức năng: Liên kết các view từ layout
     */
    private void initViews(View view) {
        etUsername = view.findViewById(R.id.et_username);
        etPassword = view.findViewById(R.id.et_password);
        cbRememberMe = view.findViewById(R.id.cb_remember_me);
        btnLogin = view.findViewById(R.id.btn_login);
        tvForgotPassword = view.findViewById(R.id.tv_forgot_password);
        progressBar = view.findViewById(R.id.progress_bar);
    }

    /**
     * Thiết lập các sự kiện click
     * Chức năng: Gán listener cho các button và text view
     */
    private void setupClickListeners() {
        // Sự kiện click nút đăng nhập
        btnLogin.setOnClickListener(v -> performLogin());

        // Sự kiện click quên mật khẩu
        tvForgotPassword.setOnClickListener(v -> {
            // TODO: Implement forgot password functionality
            if (loginListener != null) {
                loginListener.onLoginError("Tính năng quên mật khẩu sẽ được cập nhật sớm");
            }
        });
    }

    /**
     * Thực hiện đăng nhập
     * Chức năng: Validate input và gọi AuthManager để đăng nhập
     */
    private void performLogin() {
        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();

        // Kiểm tra validation input
        if (username.isEmpty()) {
            etUsername.setError("Vui lòng nhập tên đăng nhập");
            etUsername.requestFocus();
            return;
        }

        if (password.isEmpty()) {
            etPassword.setError("Vui lòng nhập mật khẩu");
            etPassword.requestFocus();
            return;
        }

        if (password.length() < 6) {
            etPassword.setError("Mật khẩu phải có ít nhất 6 ký tự");
            etPassword.requestFocus();
            return;
        }

        // Hiển thị loading
        setLoading(true);

        // Thực hiện đăng nhập thông qua AuthManager
        authManager.login(username, password, new AuthManager.AuthCallback() {
            @Override
            public void onSuccess(User user) {
                // Đăng nhập thành công
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        setLoading(false);
                        if (loginListener != null) {
                            loginListener.onLoginSuccess(user); // Gọi callback thành công
                        }
                    });
                }
            }

            @Override
            public void onError(String error) {
                // Đăng nhập thất bại
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        setLoading(false);
                        if (loginListener != null) {
                            loginListener.onLoginError(error); // Gọi callback lỗi
                        }
                    });
                }
            }
        });
    }

    /**
     * Thiết lập trạng thái loading
     * Chức năng: Hiển thị/ẩn progress bar và disable/enable button
     * @param loading true để hiển thị loading, false để ẩn
     */
    private void setLoading(boolean loading) {
        if (loading) {
            progressBar.setVisibility(View.VISIBLE);
            btnLogin.setEnabled(false);
            btnLogin.setText("Đang đăng nhập...");
        } else {
            progressBar.setVisibility(View.GONE);
            btnLogin.setEnabled(true);
            btnLogin.setText("Đăng nhập");
        }
    }
}

package com.example.myapplication.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * Utility class để xử lý mã hóa và xác thực mật khẩu
 */
public class PasswordUtils {
    
    private static final String ALGORITHM = "SHA-256";
    private static final int SALT_LENGTH = 16;
    
    /**
     * Mã hóa mật khẩu với salt
     * @param password Mật khẩu gốc
     * @return Mật khẩu đã được hash với salt
     */
    public static String hashPassword(String password) {
        try {
            // Tạo salt ngẫu nhiên
            SecureRandom random = new SecureRandom();
            byte[] salt = new byte[SALT_LENGTH];
            random.nextBytes(salt);
            
            // Hash password với salt
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            md.update(salt);
            byte[] hashedPassword = md.digest(password.getBytes());
            
            // Kết hợp salt và hash
            byte[] combined = new byte[salt.length + hashedPassword.length];
            System.arraycopy(salt, 0, combined, 0, salt.length);
            System.arraycopy(hashedPassword, 0, combined, salt.length, hashedPassword.length);
            
            // Encode thành Base64
            return Base64.getEncoder().encodeToString(combined);
            
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Error hashing password", e);
        }
    }
    
    /**
     * Xác thực mật khẩu
     * @param password Mật khẩu cần kiểm tra
     * @param hashedPassword Mật khẩu đã hash từ database
     * @return true nếu mật khẩu đúng
     */
    public static boolean verifyPassword(String password, String hashedPassword) {
        try {
            // Decode từ Base64
            byte[] combined = Base64.getDecoder().decode(hashedPassword);
            
            // Tách salt và hash
            byte[] salt = new byte[SALT_LENGTH];
            byte[] hash = new byte[combined.length - SALT_LENGTH];
            System.arraycopy(combined, 0, salt, 0, SALT_LENGTH);
            System.arraycopy(combined, SALT_LENGTH, hash, 0, hash.length);
            
            // Hash password với salt đã có
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            md.update(salt);
            byte[] testHash = md.digest(password.getBytes());
            
            // So sánh hash
            return MessageDigest.isEqual(hash, testHash);
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Kiểm tra độ mạnh của mật khẩu
     * @param password Mật khẩu cần kiểm tra
     * @return true nếu mật khẩu đủ mạnh
     */
    public static boolean isPasswordStrong(String password) {
        if (password == null || password.length() < 6) {
            return false;
        }
        
        boolean hasUpper = false;
        boolean hasLower = false;
        boolean hasDigit = false;
        boolean hasSpecial = false;
        
        for (char c : password.toCharArray()) {
            if (Character.isUpperCase(c)) {
                hasUpper = true;
            } else if (Character.isLowerCase(c)) {
                hasLower = true;
            } else if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (!Character.isLetterOrDigit(c)) {
                hasSpecial = true;
            }
        }
        
        // Yêu cầu ít nhất 3 trong 4 tiêu chí
        int criteria = 0;
        if (hasUpper) criteria++;
        if (hasLower) criteria++;
        if (hasDigit) criteria++;
        if (hasSpecial) criteria++;
        
        return criteria >= 3;
    }
}
